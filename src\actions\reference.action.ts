'use server';

import { createServerApiCall } from '@/lib/api-client';
import { TokenUtils } from '@/lib/token-utils';
import type { Amenity, CostType, Rule, AppEnums } from '@/stores/referenceStore';

// Create server API call function (reference data is public, no auth needed)
const serverApiCall = createServerApiCall(() => null);

/**
 * Get all amenities with optional category filter
 */
export async function getAmenities(category?: string): Promise<Amenity[]> {
  try {
    const endpoint = category 
      ? `/amenities?category=${category}`
      : '/amenities';
    
    const response = await serverApiCall<Amenity[]>(endpoint, {
      method: 'GET'
    });

    return response;
  } catch (error: any) {
    console.error('Failed to get amenities:', error);
    throw new Error(error.message || 'Failed to get amenities');
  }
}

/**
 * Get all cost types with optional category filter
 */
export async function getCostTypes(category?: string): Promise<CostType[]> {
  try {
    const endpoint = category 
      ? `/cost-types?category=${category}`
      : '/cost-types';
    
    const response = await serverApiCall<CostType[]>(endpoint, {
      method: 'GET'
    });

    return response;
  } catch (error: any) {
    console.error('Failed to get cost types:', error);
    throw new Error(error.message || 'Failed to get cost types');
  }
}

/**
 * Get all rules with optional category filter
 */
export async function getRules(category?: string): Promise<Rule[]> {
  try {
    const endpoint = category 
      ? `/rules?category=${category}`
      : '/rules';
    
    const response = await serverApiCall<Rule[]>(endpoint, {
      method: 'GET'
    });

    return response;
  } catch (error: any) {
    console.error('Failed to get rules:', error);
    throw new Error(error.message || 'Failed to get rules');
  }
}

/**
 * Get all application enums
 */
export async function getAppEnums(): Promise<AppEnums> {
  try {
    const response = await serverApiCall<AppEnums>('/enums', {
      method: 'GET'
    });

    return response;
  } catch (error: any) {
    console.error('Failed to get app enums:', error);
    throw new Error(error.message || 'Failed to get app enums');
  }
}
