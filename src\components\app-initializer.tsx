"use client";

import { useEffect } from 'react';
import { useUserStore } from '@/stores/userStore';
import { useReferenceStore } from '@/stores/referenceStore';

interface AppInitializerProps {
  children: React.ReactNode;
}

export function AppInitializer({ children }: AppInitializerProps) {
  const { loadUser, hasHydrated } = useUserStore();
  const { loadReferenceData, isLoaded: referenceLoaded } = useReferenceStore();

  useEffect(() => {
    // Only initialize after hydration is complete
    if (!hasHydrated) return;

    // Load reference data (public, no auth needed)
    if (!referenceLoaded) {
      loadReferenceData().catch(error => {
        console.error('Failed to load reference data:', error);
      });
    }

    // Load user data if tokens exist
    loadUser().catch(error => {
      console.error('Failed to load user data:', error);
    });
  }, [hasHydrated, referenceLoaded, loadReferenceData, loadUser]);

  return <>{children}</>;
}
