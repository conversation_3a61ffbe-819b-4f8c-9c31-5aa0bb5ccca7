"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import Image from "next/image"
import { ArrowLeft, Heart, Share2, MapPin, Star, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useRoomStore } from "@/stores/roomStore"
import { AmenitySelector } from "@/components/ui/amenity-selector"
import { CostTypeSelector } from "@/components/ui/cost-type-selector"
import { RuleSelector } from "@/components/ui/rule-selector"

export default function PropertyDetailPage() {
  const params = useParams()
  const roomSlug = params.id as string
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  const {
    currentRoom: roomDetail,
    roomLoading: isLoading,
    roomError: error,
    savedRooms,
    loadRoomDetail,
    toggleSaveRoom,
    clearRoomDetail
  } = useRoomStore()

  const isSaved = roomDetail ? savedRooms.includes(roomDetail.id) : false

  // Load room detail from API using store
  useEffect(() => {
    if (roomSlug && (!roomDetail || roomDetail.slug !== roomSlug)) {
      loadRoomDetail(roomSlug)
    }

    // Cleanup when component unmounts
    return () => {
      clearRoomDetail()
    }
  }, [roomSlug, roomDetail, loadRoomDetail, clearRoomDetail])

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-green-600" />
          <p className="text-gray-600 mt-2">Đang tải thông tin phòng...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (error || !roomDetail) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {error || 'Không tìm thấy phòng trọ'}
          </h1>
          <Button onClick={() => window.history.back()}>
            Quay lại
          </Button>
        </div>
      </div>
    )
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN').format(price)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Button 
              variant="ghost" 
              onClick={() => window.history.back()}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
            
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => roomDetail && toggleSaveRoom(roomDetail.id)}
              >
                <Heart className={`h-4 w-4 mr-2 ${isSaved ? 'fill-red-500 text-red-500' : ''}`} />
                {isSaved ? 'Đã lưu' : 'Lưu'}
              </Button>
              <Button variant="ghost" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Chia sẻ
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Image Gallery */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
              <div className="relative h-96">
                <Image
                  src={roomDetail.images[currentImageIndex] || "/placeholder-room.jpg"}
                  alt={roomDetail.title}
                  fill
                  className="object-cover"
                />
                {roomDetail.isVerified && (
                  <div className="absolute top-4 left-4">
                    <span className="bg-green-500 text-white text-sm font-bold px-3 py-1 rounded">
                      ĐÃ XÁC MINH
                    </span>
                  </div>
                )}
              </div>

              {/* Image Thumbnails */}
              {roomDetail.images.length > 1 && (
                <div className="flex gap-2 p-4 overflow-x-auto">
                  {roomDetail.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                        currentImageIndex === index ? 'border-blue-500' : 'border-gray-200'
                      }`}
                    >
                      <Image
                        src={image}
                        alt={`${roomDetail.title} ${index + 1}`}
                        width={80}
                        height={80}
                        className="object-cover w-full h-full"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Room Info */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">
                {roomDetail.title}
              </h1>

              <div className="flex items-center mb-4">
                <MapPin className="h-5 w-5 text-gray-500 mr-2" />
                <span className="text-gray-700">
                  {roomDetail.address.street}, {roomDetail.address.ward}, {roomDetail.address.district}, {roomDetail.address.province}
                </span>
              </div>

              {roomDetail.averageRating > 0 && (
                <div className="flex items-center mb-4">
                  <Star className="h-5 w-5 text-yellow-500 mr-2" />
                  <span className="font-medium">{roomDetail.averageRating.toFixed(1)}</span>
                  <span className="text-gray-500 ml-2">
                    ({roomDetail.totalReviews} đánh giá)
                  </span>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4 mb-6">
                <div>
                  <span className="text-gray-500">Diện tích:</span>
                  <span className="font-medium ml-2">{roomDetail.area}m²</span>
                </div>
                <div>
                  <span className="text-gray-500">Sức chứa:</span>
                  <span className="font-medium ml-2">{roomDetail.maxOccupancy} người</span>
                </div>
                <div>
                  <span className="text-gray-500">Loại phòng:</span>
                  <span className="font-medium ml-2">{roomDetail.roomType}</span>
                </div>
                <div>
                  <span className="text-gray-500">Trạng thái:</span>
                  <span className="font-medium ml-2 text-green-600">
                    {roomDetail.isVerified ? 'Đã xác minh' : 'Chưa xác minh'}
                  </span>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="font-semibold text-gray-900 mb-3">Mô tả</h3>
                <p className="text-gray-700">{roomDetail.description}</p>
              </div>

              {/* Amenities */}
              <div className="mb-6">
                <h3 className="font-semibold text-gray-900 mb-3">Tiện nghi</h3>
                <AmenitySelector
                  selectedAmenities={roomDetail.amenities.map(a => a.id)}
                  onSelectionChange={() => {}} // Read-only
                  mode="display"
                />
              </div>

              {/* Cost Types */}
              {roomDetail.costTypes.length > 0 && (
                <div className="mb-6">
                  <h3 className="font-semibold text-gray-900 mb-3">Chi phí phát sinh</h3>
                  <CostTypeSelector
                    selectedCostTypes={roomDetail.costTypes.map(cost => ({
                      id: cost.id,
                      name: cost.name,
                      amount: cost.amount,
                      category: cost.category,
                      unit: cost.unit
                    }))}
                    onSelectionChange={() => {}} // Read-only
                    mode="display"
                  />
                </div>
              )}

              {/* Rules */}
              {roomDetail.rules.length > 0 && (
                <div className="mb-6">
                  <h3 className="font-semibold text-gray-900 mb-3">Quy định</h3>
                  <RuleSelector
                    selectedRules={roomDetail.rules.map(r => r.id)}
                    onSelectionChange={() => {}} // Read-only
                    mode="display"
                  />
                </div>
              )}
            </div>

            {/* Reviews */}
            {roomDetail.reviews.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="font-semibold text-gray-900 mb-4">
                  Đánh giá ({roomDetail.reviews.length})
                </h3>
                <div className="space-y-4">
                  {roomDetail.reviews.map((review) => (
                    <div key={review.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                      <div className="flex items-center mb-2">
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < review.rating ? 'text-yellow-500 fill-current' : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="font-medium ml-2">
                          {review.tenant.firstName} {review.tenant.lastName}
                        </span>
                        <span className="text-gray-500 ml-2 text-sm">
                          {new Date(review.createdAt).toLocaleDateString('vi-VN')}
                        </span>
                      </div>
                      <p className="text-gray-700">{review.comment}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6 sticky top-6">
              <div className="text-3xl font-bold text-red-600 mb-4">
                {formatPrice(roomDetail.monthlyRent)} VNĐ/tháng
              </div>

              <div className="space-y-4 mb-6">
                <Button className="w-full" size="lg">
                  Liên hệ thuê phòng
                </Button>
                <Button variant="outline" className="w-full" size="lg">
                  Xem số điện thoại
                </Button>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-semibold text-gray-900 mb-3">Thông tin chủ trọ</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    {roomDetail.landlord.avatar && (
                      <Image
                        src={roomDetail.landlord.avatar}
                        alt="Avatar"
                        width={32}
                        height={32}
                        className="rounded-full"
                      />
                    )}
                    <span className="font-medium">
                      {roomDetail.landlord.firstName} {roomDetail.landlord.lastName}
                    </span>
                    {roomDetail.landlord.isVerified && (
                      <span className="text-green-600 text-xs">✓ Đã xác minh</span>
                    )}
                  </div>
                  <div>Điện thoại: {roomDetail.landlord.phone}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
