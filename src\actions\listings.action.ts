'use server';

import { createServerApiCall } from '@/lib/api-client';
import { TokenUtils } from '@/lib/token-utils';

// Types for API responses
export interface RoomListing {
  id: string;
  slug: string;
  title: string;
  description: string;
  monthlyRent: number;
  area: number;
  maxOccupancy: number;
  roomType: string;
  isVerified: boolean;
  images: string[];
  amenities: Array<{
    id: string;
    name: string;
    category: string;
  }>;
  costTypes: Array<{
    id: string;
    name: string;
    amount: number;
    category: string;
    unit: string;
  }>;
  rules: Array<{
    id: string;
    name: string;
    category: string;
  }>;
  address: {
    street: string;
    ward: string;
    district: string;
    province: string;
  };
  landlord: {
    id: string;
    firstName: string;
    lastName: string;
    phone: string;
    avatar?: string;
    isVerified: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface RoomListingsResponse {
  data: RoomListing[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface RoomDetail extends RoomListing {
  // Additional details for room detail page
  virtualTour?: string;
  nearbyPlaces: Array<{
    name: string;
    distance: number;
    type: string;
  }>;
  reviews: Array<{
    id: string;
    rating: number;
    comment: string;
    tenant: {
      firstName: string;
      lastName: string;
      avatar?: string;
    };
    createdAt: string;
  }>;
  averageRating: number;
  totalReviews: number;
}

// Search parameters interface
export interface RoomSearchParams {
  search?: string;
  provinceId?: number;
  districtId?: number;
  wardId?: number;
  roomType?: string;
  minPrice?: number;
  maxPrice?: number;
  minArea?: number;
  maxArea?: number;
  amenities?: string; // comma-separated amenity IDs
  maxOccupancy?: number;
  isVerified?: boolean;
  sortBy?: 'price' | 'area' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// Create server API call function
const serverApiCall = createServerApiCall(() => TokenUtils.getAccessToken());

/**
 * Search room listings with filters
 */
export async function searchRoomListings(params: RoomSearchParams = {}): Promise<RoomListingsResponse> {
  try {
    // Build query string
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = `/api/listings/rooms${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    
    const response = await serverApiCall<RoomListingsResponse>(endpoint, {
      method: 'GET'
    });

    return response;
  } catch (error: any) {
    console.error('Failed to search room listings:', error);
    throw new Error(error.message || 'Failed to search room listings');
  }
}

/**
 * Get room detail by slug
 */
export async function getRoomBySlug(slug: string): Promise<RoomDetail> {
  try {
    const response = await serverApiCall<RoomDetail>(`/api/rooms/${slug}`, {
      method: 'GET'
    });

    return response;
  } catch (error: any) {
    console.error('Failed to get room detail:', error);
    throw new Error(error.message || 'Failed to get room detail');
  }
}

/**
 * Get featured/hot room listings for homepage
 */
export async function getFeaturedRoomListings(limit: number = 8): Promise<RoomListing[]> {
  try {
    const response = await searchRoomListings({
      sortBy: 'createdAt',
      sortOrder: 'desc',
      limit,
      page: 1
    });

    return response.data;
  } catch (error: any) {
    console.error('Failed to get featured room listings:', error);
    throw new Error(error.message || 'Failed to get featured room listings');
  }
}
